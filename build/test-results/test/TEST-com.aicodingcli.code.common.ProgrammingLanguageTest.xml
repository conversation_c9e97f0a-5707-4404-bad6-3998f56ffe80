<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.code.common.ProgrammingLanguageTest" tests="5" skipped="0" failures="0" errors="0" timestamp="2025-06-17T16:29:56.162Z" hostname="zxnapdeMacBook-Pro.local" time="0.002">
  <properties/>
  <testcase name="should detect language from file extension()" classname="com.aicodingcli.code.common.ProgrammingLanguageTest" time="0.0"/>
  <testcase name="should have kotlin language with correct properties()" classname="com.aicodingcli.code.common.ProgrammingLanguageTest" time="0.0"/>
  <testcase name="should have java language with correct properties()" classname="com.aicodingcli.code.common.ProgrammingLanguageTest" time="0.0"/>
  <testcase name="should detect language from file path()" classname="com.aicodingcli.code.common.ProgrammingLanguageTest" time="0.0"/>
  <testcase name="should throw exception for unsupported file extension()" classname="com.aicodingcli.code.common.ProgrammingLanguageTest" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
