=== DEBUG INFO ===
fFile path: /var/folders/58/mctmb5rs4m36zm0x9zdhfy480000gn/T/junit17103174153290360115/ComplexClass.kt
Language: KOTLIN
Lines of code: 17
Cyclomatic complexity: 4
)Maintainability index: 77.83393327971892
Number of issues: 1
Number of suggestions: 2

Issues:
I- CODE_SMELL: Magic number '10' should be replaced with a named constant


Suggestions:
W- MAINTAINABILITY: Consider extracting complex conditional logic into separate methods
@- READABILITY: Consider adding documentation for public methods

=== DIRECT METHOD CALLS ===
Direct suggestions count: 2
W- MAINTAINABILITY: Consider extracting complex conditional logic into separate methods
@- READABILITY: Consider adding documentation for public methods
=== PROJECT ANALYSIS DEBUG ===
XProject path: /var/folders/58/mctmb5rs4m36zm0x9zdhfy480000gn/T/junit3559596158297332901
Files analyzed: 2
ZFile: /var/folders/58/mctmb5rs4m36zm0x9zdhfy480000gn/T/junit3559596158297332901/Class1.kt
	  LOC: 3
  Complexity: 1
%  Maintainability: 92.50693855665945
ZFile: /var/folders/58/mctmb5rs4m36zm0x9zdhfy480000gn/T/junit3559596158297332901/Class2.kt
	  LOC: 3
  Complexity: 1
%  Maintainability: 92.50693855665945
Overall metrics:
  Total LOC: 6
  Avg Complexity: 1
)  Avg Maintainability: 92.50693855665945
	Summary:
  Total Files: 2
  Average Complexity: 1.0
-  Overall Maintainability: 92.50693855665945
"!=== LINES OF CODE DEBUG ===
"!Code:
"!// This is a comment
"!class TestClass {
"!    // Another comment
"!    
"!    fun method1() {
"!        // Method comment
"!$        val x = 1 // Inline comment
"!        return x
"!    }
"!    
"!    /*
"!     * Block comment
"!     * Multiple lines
"!     */
"!    fun method2() {
"!        val y = 2
"!        return y
"!    }
"!}
"!
"!Calculated LOC: 10
"!IExpected LOC: 7 (class declaration, 2 method declarations, 4 statements)
"!'Line 1: '// This is a comment' -> SKIP
"!$Line 2: 'class TestClass {' -> CODE
"!%Line 3: '// Another comment' -> SKIP
"!Line 4: '' -> SKIP
"!"Line 5: 'fun method1() {' -> CODE
"!$Line 6: '// Method comment' -> SKIP
"!.Line 7: 'val x = 1 // Inline comment' -> CODE
"!Line 8: 'return x' -> CODE
"!Line 9: '}' -> CODE
"!Line 10: '' -> SKIP
"!Line 11: '/*' -> SKIP
"!#Line 12: '* Block comment' -> SKIP
"!$Line 13: '* Multiple lines' -> SKIP
"!Line 14: '*/' -> SKIP
"!#Line 15: 'fun method2() {' -> CODE
"!Line 16: 'val y = 2' -> CODE
"!Line 17: 'return y' -> CODE
"!Line 18: '}' -> CODE
"!Line 19: '}' -> CODE
"#$=== CYCLOMATIC COMPLEXITY DEBUG ===
"#Code:
"#class ConditionalClass {
"#/    fun processInput(input: String?): String {
"#        if (input == null) {
"#            return "null"
"#
        }
"#	        
"#        if (input.isEmpty()) {
"#            return "empty"
"#
        }
"#	        
"#        return when {
"#(            input.length > 10 -> "long"
"#)            input.length > 5 -> "medium"
"#            else -> "short"
"#
        }
"#    }
"#}
"#
"#Calculated complexity: 5
"#DExpected complexity: 6 (2 if statements + 3 when branches + 1 base)
"#Lines of code: 15
"#Duplicated lines: 0
.-Number of suggestions: 2
.-W- MAINTAINABILITY: Consider extracting complex conditional logic into separate methods
.-@- READABILITY: Consider adding documentation for public methods
