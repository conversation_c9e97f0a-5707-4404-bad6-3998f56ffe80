<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.code.analysis.CodeAnalyzerTest" tests="6" skipped="0" failures="0" errors="0" timestamp="2025-06-17T16:29:56.152Z" hostname="zxnapdeMacBook-Pro.local" time="0.01">
  <properties/>
  <testcase name="should analyze simple kotlin file()" classname="com.aicodingcli.code.analysis.CodeAnalyzerTest" time="0.001"/>
  <testcase name="should throw exception for non-existent file()" classname="com.aicodingcli.code.analysis.CodeAnalyzerTest" time="0.001"/>
  <testcase name="should detect issues in problematic code()" classname="com.aicodingcli.code.analysis.CodeAnalyzerTest" time="0.003"/>
  <testcase name="should suggest improvements for specific code patterns()" classname="com.aicodingcli.code.analysis.CodeAnalyzerTest" time="0.001"/>
  <testcase name="should detect specific issue types()" classname="com.aicodingcli.code.analysis.CodeAnalyzerTest" time="0.0"/>
  <testcase name="should analyze project directory()" classname="com.aicodingcli.code.analysis.CodeAnalyzerTest" time="0.001"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
