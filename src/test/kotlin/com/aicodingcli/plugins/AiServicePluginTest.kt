package com.aicodingcli.plugins

import com.aicodingcli.ai.*
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.flow.toList
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach

class AiServicePluginTest {
    
    private lateinit var testFramework: PluginTestFramework
    private lateinit var testPlugin: TestAiServicePlugin
    
    @BeforeEach
    fun setUp() {
        testFramework = PluginTestFramework()
        testPlugin = TestAiServicePlugin()
    }
    
    @Test
    fun `should create AI service plugin with correct metadata`() {
        assertEquals("test-ai-service-plugin", testPlugin.metadata.id)
        assertEquals("Test AI Service Plugin", testPlugin.metadata.name)
        assertEquals("1.0.0", testPlugin.metadata.version)
        assertEquals("A test AI service plugin", testPlugin.metadata.description)
        assertEquals("Test Author", testPlugin.metadata.author)
    }
    
    @Test
    fun `should support correct AI provider`() {
        assertEquals(AiProvider.OPENAI, testPlugin.supportedProvider)
    }
    
    @Test
    fun `should create AI service`() {
        val config = AiServiceConfig(
            provider = AiProvider.OPENAI,
            apiKey = "test-key",
            model = "test-model",
            baseUrl = "https://test.example.com"
        )
        
        val service = testPlugin.createAiService(config)
        assertNotNull(service)
        assertEquals(config, service.config)
        assertTrue(service is TestAiService)
    }
    
    @Test
    fun `should validate configuration successfully`() {
        val validConfig = AiServiceConfig(
            provider = AiProvider.OPENAI,
            apiKey = "test-key",
            model = "test-model",
            baseUrl = "https://test.example.com"
        )
        
        val result = testPlugin.validateConfig(validConfig)
        assertTrue(result.isValid)
        assertTrue(result.errors.isEmpty())
    }
    
    @Test
    fun `should fail validation for wrong provider`() {
        val invalidConfig = AiServiceConfig(
            provider = AiProvider.CLAUDE,
            apiKey = "test-key",
            model = "test-model",
            baseUrl = "https://test.example.com"
        )
        
        val result = testPlugin.validateConfig(invalidConfig)
        assertFalse(result.isValid)
        assertTrue(result.errors.isNotEmpty())
        assertTrue(result.errors[0].contains("Provider mismatch"))
    }
    
    @Test
    fun `should fail validation for missing API key`() {
        val invalidConfig = AiServiceConfig(
            provider = AiProvider.OPENAI,
            apiKey = "",
            model = "test-model",
            baseUrl = "https://test.example.com"
        )
        
        val result = testPlugin.validateProviderConfig(invalidConfig)
        assertFalse(result.isValid)
        assertTrue(result.errors.any { it.contains("API key is required") })
    }
    
    @Test
    fun `should provide supported models`() {
        val models = testPlugin.getSupportedModels()
        assertNotNull(models)
        assertEquals(2, models.size)
        assertTrue(models.contains("test-model-1"))
        assertTrue(models.contains("test-model-2"))
    }
    
    @Test
    fun `should provide default configuration`() {
        val defaultConfig = testPlugin.getDefaultConfig()
        assertNotNull(defaultConfig)
        assertEquals(AiProvider.OPENAI, defaultConfig?.provider)
        assertEquals("https://test.example.com", defaultConfig?.baseUrl)
        assertEquals("test-model-1", defaultConfig?.model)
        assertEquals("", defaultConfig?.apiKey) // Empty by default
    }
    
    @Test
    fun `should initialize and shutdown correctly`() {
        val context = testFramework.createTestContext(testPlugin.metadata)
        
        testPlugin.initialize(context)
        assertTrue(testPlugin.isInitialized())
        
        testPlugin.shutdown()
        assertFalse(testPlugin.isInitialized())
    }
    
    @Test
    fun `should validate plugin successfully`() {
        val result = testFramework.testPluginValidation(testPlugin)
        
        assertTrue(result.success)
        assertTrue(result.errors.isEmpty())
    }
    
    @Test
    fun `should register with AI service plugin registry`() {
        AiServicePluginRegistry.register(testPlugin)
        
        val registeredPlugin = AiServicePluginRegistry.getPlugin(AiProvider.OPENAI)
        assertNotNull(registeredPlugin)
        assertEquals(testPlugin.metadata.id, registeredPlugin?.metadata?.id)
        
        assertTrue(AiServicePluginRegistry.isProviderSupported(AiProvider.OPENAI))
        assertFalse(AiServicePluginRegistry.isProviderSupported(AiProvider.CLAUDE))
        
        val allPlugins = AiServicePluginRegistry.getAllPlugins()
        assertTrue(allPlugins.containsKey(AiProvider.OPENAI))
        
        AiServicePluginRegistry.unregister(AiProvider.OPENAI)
        assertNull(AiServicePluginRegistry.getPlugin(AiProvider.OPENAI))
    }
    
    @Test
    fun `should handle AI service operations`() = runBlocking {
        val config = AiServiceConfig(
            provider = AiProvider.OPENAI,
            apiKey = "test-key",
            model = "test-model",
            baseUrl = "https://test.example.com"
        )
        
        val service = testPlugin.createAiService(config)
        
        // Test chat
        val request = AiRequest(
            messages = listOf(AiMessage(MessageRole.USER, "Hello")),
            model = "test-model"
        )
        
        val response = service.chat(request)
        assertEquals("Test response: Hello", response.content)
        assertEquals("test-model", response.model)
        assertEquals(FinishReason.STOP, response.finishReason)
        
        // Test streaming
        val streamChunks = service.streamChat(request).toList()
        assertEquals(3, streamChunks.size)
        assertEquals("Test ", streamChunks[0].content)
        assertEquals("streaming ", streamChunks[1].content)
        assertEquals("response", streamChunks[2].content)
        assertEquals(FinishReason.STOP, streamChunks[2].finishReason)
        
        // Test connection
        assertTrue(service.testConnection())
    }
}

/**
 * Test implementation of AiServicePlugin for testing purposes
 */
class TestAiServicePlugin : BaseAiServicePlugin() {
    
    override val metadata = PluginMetadata(
        id = "test-ai-service-plugin",
        name = "Test AI Service Plugin",
        version = "1.0.0",
        description = "A test AI service plugin",
        author = "Test Author",
        mainClass = "com.aicodingcli.plugins.TestAiServicePlugin"
    )
    
    override val supportedProvider = AiProvider.OPENAI
    
    override fun createAiService(config: AiServiceConfig): AiService {
        return TestAiService(config)
    }
    
    override fun getSupportedModels(): List<String> {
        return listOf("test-model-1", "test-model-2")
    }
    
    override fun getDefaultBaseUrl(): String {
        return "https://test.example.com"
    }
    
    override fun getDefaultModel(): String {
        return "test-model-1"
    }
    
    override fun requiresApiKey(): Boolean {
        return true
    }
}

/**
 * Test implementation of AiService for testing purposes
 */
class TestAiService(override val config: AiServiceConfig) : BaseAiService(config) {
    
    override suspend fun chat(request: AiRequest): AiResponse {
        validateRequest(request)
        
        return AiResponse(
            content = "Test response: ${request.messages.last().content}",
            model = request.model,
            usage = TokenUsage(10, 20, 30),
            finishReason = FinishReason.STOP
        )
    }
    
    override suspend fun streamChat(request: AiRequest): kotlinx.coroutines.flow.Flow<AiStreamChunk> {
        validateRequest(request)
        
        return kotlinx.coroutines.flow.flowOf(
            AiStreamChunk("Test ", null),
            AiStreamChunk("streaming ", null),
            AiStreamChunk("response", FinishReason.STOP)
        )
    }
    
    override suspend fun testConnection(): Boolean {
        return true
    }
}
